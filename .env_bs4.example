# XactAnalysis Beautiful Soup Automation Credentials
# Copy this file to .env and fill in your actual credentials
# NEVER commit the .env file to version control

# XactAnalysis Login Credentials
XACT_USERNAME=your_username_here
XACT_PASSWORD=your_password_here

# Optional: Configuration Overrides
# LOG_LEVEL=DEBUG
# XACTANALYSIS_BASE_URL=https://g1.xactanalysis.com
# OUTPUT_DIR=output
# CSV_CLAIM_COLUMN=1
# CSV_STATUS_COLUMN=0
# CSV_COMPLETION_MARKER=D

# Web Application Settings
# WEB_HOST=127.0.0.1
# WEB_PORT=5001
# WEB_DEBUG=false

# Performance Settings
# CLAIM_PROCESSING_DELAY=1
# MAX_BATCH_SIZE=100
# REQUEST_TIMEOUT=30

# Security Settings
# SESSION_TIMEOUT=3600
# MAX_CONCURRENT_REQUESTS=5
