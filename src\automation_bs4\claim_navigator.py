"""
Claim search and navigation module for XactAnalysis using Beautiful Soup.

This module handles searching for claims and navigating to the notes section
using HTTP requests and HTML parsing.
"""

import time
from datetime import datetime, timedelta
from typing import Optional, List, Dict, Any
from urllib.parse import urljoin
from bs4 import BeautifulSoup, Tag

from .xact_session import XactSession
from ..utils.config_manager import ConfigManager
from ..utils.logger import get_logger
from ..utils.exceptions import ClaimNotFoundException, NavigationError


class ClaimNavigator:
    """Handles claim search and navigation in XactAnalysis."""
    
    def __init__(self, session: XactSession, config_manager: Optional[ConfigManager] = None):
        """
        Initialize the claim navigator.
        
        Args:
            session: Authenticated XactAnalysis session
            config_manager: Configuration manager instance
        """
        self.session = session
        self.config = config_manager or ConfigManager()
        self.logger = get_logger(__name__, self.config)
        
        # Configuration
        self.search_delay = self.config.get_float('xactanalysis', 'search_delay', 2.0)
        self.nav_delay = self.config.get_float('xactanalysis', 'nav_delay', 1.0)
        self.date_range_years = self.config.get_int('xactanalysis', 'search_date_range_years', 3)
        
        self.logger.info("Claim navigator initialized")
    
    def search_claim(self, claim_number: str) -> List[Dict[str, Any]]:
        """
        Search for a claim number in XactAnalysis.
        
        Args:
            claim_number: The claim number to search for
            
        Returns:
            List of claim results with metadata
            
        Raises:
            ClaimNotFoundException: If no claims are found
            NavigationError: If search fails
        """
        try:
            self.logger.info(f"Searching for claim: {claim_number}")
            
            # Step 1: Find the search functionality
            search_url, search_params = self._find_search_endpoint(claim_number)
            
            # Step 2: Perform the search
            search_response = self._perform_search(search_url, search_params, claim_number)
            
            # Step 3: Parse search results
            claims = self._parse_search_results(search_response, claim_number)
            
            if not claims:
                raise ClaimNotFoundException(f"No claims found for claim number: {claim_number}")
            
            self.logger.info(f"Found {len(claims)} claim(s) for {claim_number}")
            return claims
            
        except Exception as e:
            if isinstance(e, (ClaimNotFoundException, NavigationError)):
                raise
            self.logger.error(f"Search failed for claim {claim_number}: {e}")
            raise NavigationError(f"Search failed: {e}")
    
    def _find_search_endpoint(self, claim_number: str) -> tuple[str, Dict[str, Any]]:
        """
        Find the search endpoint and required parameters.

        Args:
            claim_number: Claim number to search for

        Returns:
            Tuple of (search_url, search_parameters)
        """
        # Try to get the main XactAnalysis page to find search functionality
        main_response = self.session.get('https://www.xactanalysis.com')
        soup = BeautifulSoup(main_response.content, 'html.parser')

        # Look for search forms
        search_forms = []

        # Find forms that might be search forms
        for form in soup.find_all('form'):
            form_inputs = form.find_all('input')

            # Check if this looks like a search form
            has_search_input = any(
                input_field.get('type') in ['search', 'text'] and
                any(keyword in str(input_field.get('name', '')).lower() + str(input_field.get('placeholder', '')).lower()
                    for keyword in ['search', 'claim', 'number', 'find'])
                for input_field in form_inputs
            )

            if has_search_input:
                search_forms.append(form)
        
        if not search_forms:
            # Look for search links or AJAX endpoints
            search_links = soup.find_all('a', href=lambda href: href and 'search' in href.lower())
            if search_links:
                search_url = urljoin(self.session.base_url, search_links[0]['href'])
                return search_url, {'q': claim_number, 'query': claim_number, 'search': claim_number}
            
            # Fallback: try common search endpoints
            common_endpoints = [
                '/search',
                '/claims/search',
                '/api/search',
                '/search.php',
                '/search.aspx'
            ]
            
            for endpoint in common_endpoints:
                try:
                    test_response = self.session.get(endpoint)
                    if test_response.status_code == 200:
                        return urljoin(self.session.base_url, endpoint), {'q': claim_number}
                except:
                    continue
            
            raise NavigationError("Could not find search functionality on the page")
        
        # Use the first search form found
        search_form = search_forms[0]
        form_action = search_form.get('action', '/search')
        form_method = search_form.get('method', 'get').lower()
        
        search_url = urljoin('https://www.xactanalysis.com', form_action)
        
        # Build search parameters from form inputs
        search_params = {}
        for input_field in search_form.find_all('input'):
            field_name = input_field.get('name')
            field_type = input_field.get('type', '').lower()
            field_value = input_field.get('value', '')

            if not field_name:
                continue

            if field_type in ['search', 'text'] and any(keyword in field_name.lower()
                                                       for keyword in ['search', 'claim', 'number', 'query']):
                search_params[field_name] = claim_number
            elif field_type == 'hidden':
                search_params[field_name] = field_value

        # Also check for select elements (dropdowns) that might control date ranges
        for select_field in search_form.find_all('select'):
            field_name = select_field.get('name')
            if field_name and any(keyword in field_name.lower()
                                 for keyword in ['date', 'range', 'period', 'time']):
                # Look for "3 years" or similar options
                for option in select_field.find_all('option'):
                    option_text = option.get_text().lower()
                    option_value = option.get('value', '')
                    if any(keyword in option_text for keyword in ['3 year', 'three year', 'last 3']):
                        search_params[field_name] = option_value
                        break
                    elif 'year' in option_text and '3' in option_text:
                        search_params[field_name] = option_value
                        break
        
        # If no specific search field found, try common parameter names
        if not any('search' in key.lower() or 'claim' in key.lower() or 'query' in key.lower()
                  for key in search_params.keys()):
            search_params.update({
                'q': claim_number,
                'query': claim_number,
                'search': claim_number,
                'claim': claim_number,
                'claimNumber': claim_number
            })

        # Add date range parameters for "last 3 years"
        search_params.update(self._get_date_range_params())

        return search_url, search_params

    def _get_date_range_params(self) -> Dict[str, str]:
        """
        Get date range parameters for "last 3 years" search.

        Returns:
            Dictionary with date range parameters
        """
        # Calculate dates for configured date range
        end_date = datetime.now()
        start_date = end_date - timedelta(days=self.date_range_years*365)  # Configurable years

        # Format dates in common formats used by web forms
        date_formats = {
            # Common date parameter names and their formatted values
            'startDate': start_date.strftime('%Y-%m-%d'),
            'endDate': end_date.strftime('%Y-%m-%d'),
            'start_date': start_date.strftime('%Y-%m-%d'),
            'end_date': end_date.strftime('%Y-%m-%d'),
            'dateFrom': start_date.strftime('%Y-%m-%d'),
            'dateTo': end_date.strftime('%Y-%m-%d'),
            'date_from': start_date.strftime('%Y-%m-%d'),
            'date_to': end_date.strftime('%Y-%m-%d'),
            # Alternative formats
            'startDate_formatted': start_date.strftime('%m/%d/%Y'),
            'endDate_formatted': end_date.strftime('%m/%d/%Y'),
            'fromDate': start_date.strftime('%Y-%m-%d'),
            'toDate': end_date.strftime('%Y-%m-%d'),
            # Range selector (common in XactAnalysis-type systems)
            'dateRange': 'last3years',
            'date_range': 'last3years',
            'timeRange': '3years',
            'period': 'last3years'
        }

        self.logger.debug(f"Date range: {start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')}")

        return date_formats

    def _perform_search(self, search_url: str, search_params: Dict[str, Any], claim_number: str) -> Any:
        """
        Perform the actual search request.
        
        Args:
            search_url: URL to search
            search_params: Search parameters
            claim_number: Claim number being searched
            
        Returns:
            Search response
        """
        self.logger.debug(f"Searching at {search_url} with params: {search_params}")
        
        # Add delay to avoid being too fast
        time.sleep(self.search_delay)
        
        # Try GET request first (most common for search)
        try:
            response = self.session.get(search_url, params=search_params)
            return response
        except Exception as e:
            self.logger.warning(f"GET search failed, trying POST: {e}")
            
            # Try POST if GET fails
            try:
                response = self.session.post(search_url, data=search_params)
                return response
            except Exception as e2:
                raise NavigationError(f"Both GET and POST search requests failed: {e2}")
    
    def _parse_search_results(self, response: Any, claim_number: str) -> List[Dict[str, Any]]:
        """
        Parse search results from the response.
        
        Args:
            response: Search response
            claim_number: Original claim number searched
            
        Returns:
            List of parsed claim results
        """
        soup = BeautifulSoup(response.content, 'html.parser')
        claims = []
        
        # Look for results in various common structures
        result_containers = [
            soup.find_all('tr'),  # Table rows
            soup.find_all('div', class_=lambda cls: cls and any(word in str(cls).lower() 
                                                               for word in ['result', 'claim', 'item', 'row'])),
            soup.find_all('li'),  # List items
            soup.find_all('article'),  # Article elements
        ]
        
        for container_list in result_containers:
            for container in container_list:
                if self._contains_claim_number(container, claim_number):
                    claim_data = self._extract_claim_data(container, claim_number)
                    if claim_data:
                        claims.append(claim_data)
        
        # Remove duplicates based on URL or ID
        unique_claims = []
        seen_urls = set()
        
        for claim in claims:
            claim_url = claim.get('url', '')
            if claim_url and claim_url not in seen_urls:
                unique_claims.append(claim)
                seen_urls.add(claim_url)
            elif not claim_url:
                unique_claims.append(claim)
        
        return unique_claims
    
    def _contains_claim_number(self, element: Tag, claim_number: str) -> bool:
        """
        Check if an element contains the claim number.
        
        Args:
            element: BeautifulSoup element to check
            claim_number: Claim number to look for
            
        Returns:
            True if element contains the claim number
        """
        element_text = element.get_text().strip()
        return claim_number in element_text
    
    def _extract_claim_data(self, element: Tag, claim_number: str) -> Optional[Dict[str, Any]]:
        """
        Extract claim data from a result element.
        
        Args:
            element: BeautifulSoup element containing claim data
            claim_number: Original claim number
            
        Returns:
            Dictionary with claim data or None
        """
        try:
            # Find links within the element
            links = element.find_all('a', href=True)
            
            claim_data = {
                'claim_number': claim_number,
                'text': element.get_text().strip(),
                'url': None,
                'notes_url': None,
                'element_html': str(element)
            }
            
            # Look for the main claim link
            for link in links:
                href = link['href']
                link_text = link.get_text().strip()
                
                # Check if this link is related to the claim
                if (claim_number in link_text or 
                    claim_number in href or
                    any(keyword in href.lower() for keyword in ['claim', 'detail', 'view'])):
                    
                    claim_data['url'] = urljoin(self.session.base_url, href)
                    
                    # Look for notes-specific links
                    if 'note' in href.lower() or 'note' in link_text.lower():
                        claim_data['notes_url'] = urljoin(self.session.base_url, href)
                    
                    break
            
            return claim_data if claim_data['url'] else None
            
        except Exception as e:
            self.logger.warning(f"Failed to extract claim data from element: {e}")
            return None
    
    def navigate_to_notes(self, claim_data: Dict[str, Any]) -> str:
        """
        Navigate to the notes section for a claim.
        
        Args:
            claim_data: Claim data from search results
            
        Returns:
            URL of the notes page
            
        Raises:
            NavigationError: If navigation fails
        """
        try:
            claim_number = claim_data['claim_number']
            self.logger.info(f"Navigating to notes for claim: {claim_number}")
            
            # If we already have a direct notes URL, use it
            if claim_data.get('notes_url'):
                notes_url = claim_data['notes_url']
                self.logger.debug(f"Using direct notes URL: {notes_url}")
                return notes_url
            
            # Otherwise, navigate to the claim page and find notes
            claim_url = claim_data['url']
            if not claim_url:
                raise NavigationError("No claim URL available")
            
            # Add navigation delay
            time.sleep(self.nav_delay)
            
            # Get the claim detail page
            claim_response = self.session.get(claim_url)
            soup = BeautifulSoup(claim_response.content, 'html.parser')
            
            # Look for notes links/tabs
            notes_links = []
            
            # Common selectors for notes
            notes_selectors = [
                'a[href*="note"]',
                'a[href*="Note"]',
                'a:contains("Notes")',
                'a:contains("notes")',
                'button:contains("Notes")',
                'tab:contains("Notes")',
                '[data-tab*="note"]',
                '[data-section*="note"]'
            ]
            
            for selector in notes_selectors:
                try:
                    elements = soup.select(selector)
                    for element in elements:
                        href = element.get('href')
                        if href:
                            notes_links.append(urljoin(self.session.base_url, href))
                except:
                    continue
            
            # Also look for text-based navigation
            for link in soup.find_all('a', href=True):
                link_text = link.get_text().strip().lower()
                if 'note' in link_text:
                    notes_links.append(urljoin(self.session.base_url, link['href']))
            
            if notes_links:
                notes_url = notes_links[0]  # Use the first notes link found
                self.logger.debug(f"Found notes URL: {notes_url}")
                return notes_url
            
            # If no specific notes link found, the current page might already show notes
            # or we might need to look for notes content on the current page
            if self._page_contains_notes(soup):
                self.logger.debug("Current page appears to contain notes")
                return claim_url
            
            raise NavigationError(f"Could not find notes section for claim {claim_number}")
            
        except Exception as e:
            if isinstance(e, NavigationError):
                raise
            self.logger.error(f"Failed to navigate to notes: {e}")
            raise NavigationError(f"Navigation to notes failed: {e}")
    
    def _page_contains_notes(self, soup: BeautifulSoup) -> bool:
        """
        Check if the current page contains notes content.
        
        Args:
            soup: BeautifulSoup object of the page
            
        Returns:
            True if page appears to contain notes
        """
        # Look for notes-related content
        notes_indicators = [
            soup.find(text=lambda text: text and 'note' in text.lower()),
            soup.find('div', class_=lambda cls: cls and 'note' in str(cls).lower()),
            soup.find('section', class_=lambda cls: cls and 'note' in str(cls).lower()),
            soup.find('textarea'),  # Notes are often in text areas
            soup.find('div', {'id': lambda id_val: id_val and 'note' in id_val.lower()}),
        ]
        
        return any(notes_indicators)
