"""
Manual session manager that extracts session from user's browser.

This module allows users to login manually in their browser, then extracts
the session cookies for use with Beautiful Soup automation.
"""

import os
import json
import time
import requests
import webbrowser
from pathlib import Path
from typing import Optional, Dict, Any, List
from bs4 import BeautifulSoup

from ..utils.config_manager import ConfigManager
from ..utils.logger import get_logger
from ..utils.exceptions import LoginFailedException, NavigationError


class ManualSession:
    """Session manager using manual login and cookie extraction."""
    
    def __init__(self, config_manager: Optional[ConfigManager] = None):
        """
        Initialize the manual session manager.
        
        Args:
            config_manager: Configuration manager instance
        """
        self.config = config_manager or ConfigManager()
        self.logger = get_logger(__name__, self.config)
        
        # Session management
        self.session = requests.Session()
        self.is_authenticated = False
        self.xact_main_url = 'https://www.xactanalysis.com'
        self.login_url = 'https://identity.verisk.com/ui/login'
        
        # Cookie storage
        self.cookie_file = Path('session_cookies.json')
        
        # Configure session
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        })
        
        self.timeout = self.config.get_int('browser', 'timeout', 30000) / 1000
        
        self.logger.info("Manual session manager initialized")
    
    def start_manual_login(self) -> Dict[str, Any]:
        """
        Start the manual login process by opening the browser.
        
        Returns:
            Dictionary with instructions for the user
        """
        try:
            self.logger.info("Starting manual login process")
            
            # Open the XactAnalysis main page in the user's browser
            webbrowser.open(self.xact_main_url)
            
            return {
                'success': True,
                'message': 'Browser opened for manual login',
                'instructions': [
                    '1. Login to XactAnalysis in the browser that just opened',
                    '2. Make sure you can see the XactAnalysis dashboard',
                    '3. Come back here and click "Extract Session" when logged in',
                    '4. Keep the browser window open during processing'
                ],
                'login_url': self.xact_main_url
            }
            
        except Exception as e:
            self.logger.error(f"Failed to start manual login: {e}")
            return {
                'success': False,
                'message': f'Failed to open browser: {str(e)}'
            }
    
    def extract_session_from_browser(self) -> Dict[str, Any]:
        """
        Extract session cookies from the user's browser.
        
        This method provides instructions for manual cookie extraction.
        
        Returns:
            Dictionary with extraction results
        """
        try:
            self.logger.info("Preparing for manual session extraction")
            
            return {
                'success': True,
                'message': 'Ready for manual cookie extraction',
                'instructions': [
                    'MANUAL COOKIE EXTRACTION STEPS:',
                    '',
                    '1. In your XactAnalysis browser window, press F12 to open Developer Tools',
                    '2. Go to the "Application" tab (or "Storage" in Firefox)',
                    '3. Click on "Cookies" in the left sidebar',
                    '4. Click on "https://www.xactanalysis.com"',
                    '5. Copy ALL the cookies and paste them in the text area below',
                    '',
                    'COOKIE FORMAT NEEDED:',
                    'Name: cookie_name | Value: cookie_value',
                    'Name: another_cookie | Value: another_value',
                    '',
                    'OR paste the cookies as JSON format from browser export'
                ],
                'alternative_method': 'You can also use the browser extension method (see documentation)'
            }
            
        except Exception as e:
            self.logger.error(f"Session extraction preparation failed: {e}")
            return {
                'success': False,
                'message': f'Failed to prepare extraction: {str(e)}'
            }
    
    def load_cookies_from_text(self, cookies_text: str) -> Dict[str, Any]:
        """
        Load cookies from user-provided text.
        
        Args:
            cookies_text: Cookies in various formats
            
        Returns:
            Dictionary with loading results
        """
        try:
            self.logger.info("Loading cookies from user text")
            
            cookies_loaded = 0
            
            # Try to parse as JSON first
            try:
                cookies_data = json.loads(cookies_text)
                if isinstance(cookies_data, list):
                    # Browser export format
                    for cookie in cookies_data:
                        if isinstance(cookie, dict) and 'name' in cookie and 'value' in cookie:
                            self.session.cookies.set(
                                cookie['name'],
                                cookie['value'],
                                domain=cookie.get('domain', '.xactanalysis.com'),
                                path=cookie.get('path', '/'),
                                secure=cookie.get('secure', True)
                            )
                            cookies_loaded += 1
                elif isinstance(cookies_data, dict):
                    # Simple name-value pairs
                    for name, value in cookies_data.items():
                        self.session.cookies.set(name, value, domain='.xactanalysis.com')
                        cookies_loaded += 1
                        
            except json.JSONDecodeError:
                # Parse as text format
                lines = cookies_text.strip().split('\n')
                for line in lines:
                    line = line.strip()
                    if not line or line.startswith('#'):
                        continue
                    
                    # Try different formats
                    if '|' in line:
                        # Format: Name: cookie_name | Value: cookie_value
                        parts = line.split('|')
                        if len(parts) >= 2:
                            name_part = parts[0].strip()
                            value_part = parts[1].strip()
                            
                            if name_part.startswith('Name:'):
                                name = name_part.replace('Name:', '').strip()
                            else:
                                name = name_part
                            
                            if value_part.startswith('Value:'):
                                value = value_part.replace('Value:', '').strip()
                            else:
                                value = value_part
                            
                            if name and value:
                                self.session.cookies.set(name, value, domain='.xactanalysis.com')
                                cookies_loaded += 1
                    
                    elif '=' in line:
                        # Format: cookie_name=cookie_value
                        name, value = line.split('=', 1)
                        name = name.strip()
                        value = value.strip()
                        if name and value:
                            self.session.cookies.set(name, value, domain='.xactanalysis.com')
                            cookies_loaded += 1
                    
                    elif ':' in line:
                        # Format: cookie_name: cookie_value
                        name, value = line.split(':', 1)
                        name = name.strip()
                        value = value.strip()
                        if name and value:
                            self.session.cookies.set(name, value, domain='.xactanalysis.com')
                            cookies_loaded += 1
            
            if cookies_loaded == 0:
                return {
                    'success': False,
                    'message': 'No valid cookies found in the provided text',
                    'help': 'Please check the format and try again'
                }
            
            # Save cookies to file for future use
            self._save_cookies_to_file()
            
            # Test the session
            test_result = self._test_session()
            
            if test_result['success']:
                self.is_authenticated = True
                self.logger.info(f"Successfully loaded {cookies_loaded} cookies and verified session")
                return {
                    'success': True,
                    'message': f'Successfully loaded {cookies_loaded} cookies and verified session',
                    'cookies_loaded': cookies_loaded,
                    'session_valid': True
                }
            else:
                return {
                    'success': False,
                    'message': f'Loaded {cookies_loaded} cookies but session verification failed',
                    'cookies_loaded': cookies_loaded,
                    'session_valid': False,
                    'test_error': test_result.get('message', 'Unknown error')
                }
                
        except Exception as e:
            self.logger.error(f"Failed to load cookies: {e}")
            return {
                'success': False,
                'message': f'Failed to load cookies: {str(e)}'
            }
    
    def load_saved_cookies(self) -> Dict[str, Any]:
        """
        Load previously saved cookies from file.
        
        Returns:
            Dictionary with loading results
        """
        try:
            if not self.cookie_file.exists():
                return {
                    'success': False,
                    'message': 'No saved cookies found'
                }
            
            self.logger.info("Loading saved cookies")
            
            with open(self.cookie_file, 'r') as f:
                cookies_data = json.load(f)
            
            cookies_loaded = 0
            for cookie in cookies_data:
                self.session.cookies.set(
                    cookie['name'],
                    cookie['value'],
                    domain=cookie.get('domain'),
                    path=cookie.get('path'),
                    secure=cookie.get('secure', False)
                )
                cookies_loaded += 1
            
            # Test the session
            test_result = self._test_session()
            
            if test_result['success']:
                self.is_authenticated = True
                self.logger.info(f"Successfully loaded {cookies_loaded} saved cookies")
                return {
                    'success': True,
                    'message': f'Successfully loaded {cookies_loaded} saved cookies',
                    'cookies_loaded': cookies_loaded
                }
            else:
                return {
                    'success': False,
                    'message': 'Saved cookies are no longer valid',
                    'suggestion': 'Please login manually again'
                }
                
        except Exception as e:
            self.logger.error(f"Failed to load saved cookies: {e}")
            return {
                'success': False,
                'message': f'Failed to load saved cookies: {str(e)}'
            }
    
    def _save_cookies_to_file(self):
        """Save current cookies to file for future use."""
        try:
            cookies_data = []
            for cookie in self.session.cookies:
                cookies_data.append({
                    'name': cookie.name,
                    'value': cookie.value,
                    'domain': cookie.domain,
                    'path': cookie.path,
                    'secure': cookie.secure
                })
            
            with open(self.cookie_file, 'w') as f:
                json.dump(cookies_data, f, indent=2)
            
            self.logger.debug(f"Saved {len(cookies_data)} cookies to file")
            
        except Exception as e:
            self.logger.warning(f"Failed to save cookies: {e}")
    
    def _test_session(self) -> Dict[str, Any]:
        """
        Test if the current session is valid.
        
        Returns:
            Dictionary with test results
        """
        try:
            self.logger.debug("Testing session validity")
            
            # Try to access XactAnalysis main page
            response = self.session.get(self.xact_main_url, timeout=self.timeout)
            response.raise_for_status()
            
            # Check if we're logged in by looking for indicators
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Look for login indicators (if we see these, we're NOT logged in)
            login_indicators = [
                soup.find(text=lambda text: text and 'login' in text.lower()),
                soup.find(text=lambda text: text and 'sign in' in text.lower()),
                'identity.verisk.com' in response.url
            ]
            
            # Look for authenticated indicators
            auth_indicators = [
                soup.find(text=lambda text: text and 'dashboard' in text.lower()),
                soup.find(text=lambda text: text and 'logout' in text.lower()),
                soup.find('nav'),
                len(soup.find_all('a')) > 5  # Authenticated pages usually have more links
            ]
            
            if any(login_indicators):
                return {
                    'success': False,
                    'message': 'Session appears to be invalid - redirected to login'
                }
            
            if any(auth_indicators):
                return {
                    'success': True,
                    'message': 'Session appears to be valid'
                }
            
            # Default to success if we can't determine
            return {
                'success': True,
                'message': 'Session test completed'
            }
            
        except Exception as e:
            return {
                'success': False,
                'message': f'Session test failed: {str(e)}'
            }
    
    def get(self, url: str, **kwargs) -> requests.Response:
        """Make authenticated GET request."""
        if not self.is_authenticated:
            raise NavigationError("Not authenticated. Please complete manual login first.")
        
        if not url.startswith('http'):
            if url.startswith('/'):
                url = f"{self.xact_main_url}{url}"
            else:
                url = f"{self.xact_main_url}/{url}"
        
        response = self.session.get(url, timeout=self.timeout, **kwargs)
        response.raise_for_status()
        return response
    
    def post(self, url: str, **kwargs) -> requests.Response:
        """Make authenticated POST request."""
        if not self.is_authenticated:
            raise NavigationError("Not authenticated. Please complete manual login first.")
        
        if not url.startswith('http'):
            if url.startswith('/'):
                url = f"{self.xact_main_url}{url}"
            else:
                url = f"{self.xact_main_url}/{url}"
        
        response = self.session.post(url, timeout=self.timeout, **kwargs)
        response.raise_for_status()
        return response
    
    def close(self):
        """Close the session and clean up resources."""
        if self.session:
            self.session.close()
        self.is_authenticated = False
        self.logger.info("Manual session closed")
