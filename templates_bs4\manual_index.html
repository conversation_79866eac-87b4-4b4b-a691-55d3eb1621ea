<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>XactAnalysis Manual Login Processor</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
        }
        .status-connected { background-color: #28a745; }
        .status-disconnected { background-color: #dc3545; }
        .activity-log {
            height: 300px;
            overflow-y: auto;
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
            padding: 1rem;
        }
        .log-entry {
            margin-bottom: 0.5rem;
            padding: 0.25rem 0;
            border-bottom: 1px solid #e9ecef;
        }
        .log-entry:last-child {
            border-bottom: none;
        }
        .upload-area {
            border: 2px dashed #dee2e6;
            border-radius: 0.375rem;
            padding: 2rem;
            text-align: center;
            transition: border-color 0.3s;
        }
        .upload-area:hover {
            border-color: #0d6efd;
        }
        .upload-area.dragover {
            border-color: #0d6efd;
            background-color: #f8f9fa;
        }
        .cookie-textarea {
            height: 200px;
            font-family: monospace;
            font-size: 12px;
        }
        .step-card {
            border-left: 4px solid #0d6efd;
        }
        .step-number {
            background-color: #0d6efd;
            color: white;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <!-- Header -->
        <div class="row">
            <div class="col-12">
                <nav class="navbar navbar-dark bg-primary">
                    <div class="container-fluid">
                        <span class="navbar-brand mb-0 h1">
                            <i class="fas fa-user-cog me-2"></i>
                            XactAnalysis Manual Login Processor
                        </span>
                        <div class="d-flex align-items-center">
                            <span id="login-status" class="badge bg-secondary me-2">
                                <span class="status-indicator status-disconnected"></span>
                                Not Logged In
                            </span>
                            <span id="current-time" class="text-light">{{ current_time }}</span>
                        </div>
                    </div>
                </nav>
            </div>
        </div>

        <!-- Main Content -->
        <div class="row mt-4">
            <!-- Left Panel - Steps -->
            <div class="col-md-8">
                <!-- Step 1: CSV Upload -->
                <div class="card mb-4 step-card">
                    <div class="card-header bg-light">
                        <h5 class="card-title mb-0">
                            <span class="step-number">1</span>
                            <span class="ms-2">Upload CSV File</span>
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="upload-area" id="upload-area">
                            <i class="fas fa-cloud-upload-alt fa-3x text-muted mb-3"></i>
                            <h6>Drag & Drop CSV File Here</h6>
                            <p class="text-muted">or click to browse</p>
                            <input type="file" id="csv-file-input" accept=".csv" style="display: none;">
                            <button class="btn btn-outline-primary" onclick="document.getElementById('csv-file-input').click()">
                                <i class="fas fa-folder-open me-2"></i>Browse Files
                            </button>
                        </div>
                        <div id="file-info" class="mt-3" style="display: none;">
                            <div class="alert alert-info">
                                <strong>File loaded:</strong> <span id="file-name"></span><br>
                                <strong>Claims:</strong> <span id="file-claims"></span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Step 2: Manual Login -->
                <div class="card mb-4 step-card">
                    <div class="card-header bg-light">
                        <h5 class="card-title mb-0">
                            <span class="step-number">2</span>
                            <span class="ms-2">Manual Login to XactAnalysis</span>
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>Why Manual Login?</strong> XactAnalysis uses advanced JavaScript authentication that requires manual login for reliability.
                        </div>
                        
                        <div class="d-grid gap-2 mb-3">
                            <button id="start-login-btn" class="btn btn-warning btn-lg" disabled>
                                <i class="fas fa-external-link-alt me-2"></i>Open XactAnalysis Login
                            </button>
                            <button id="try-saved-session-btn" class="btn btn-outline-secondary">
                                <i class="fas fa-history me-2"></i>Try Saved Session
                            </button>
                        </div>
                        
                        <div id="login-instructions" class="alert alert-warning" style="display: none;">
                            <h6><i class="fas fa-list-ol me-2"></i>Login Instructions:</h6>
                            <ol class="mb-0">
                                <li>Login to XactAnalysis in the browser window that opened</li>
                                <li>Make sure you can see the XactAnalysis dashboard</li>
                                <li>Keep the browser window open</li>
                                <li>Come back here and click "Extract Session" below</li>
                            </ol>
                        </div>
                    </div>
                </div>

                <!-- Step 3: Session Extraction -->
                <div class="card mb-4 step-card">
                    <div class="card-header bg-light">
                        <h5 class="card-title mb-0">
                            <span class="step-number">3</span>
                            <span class="ms-2">Extract Session from Browser</span>
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2 mb-3">
                            <button id="extract-session-btn" class="btn btn-success" disabled>
                                <i class="fas fa-download me-2"></i>Get Cookie Instructions
                            </button>
                        </div>
                        
                        <div id="cookie-instructions" style="display: none;">
                            <div class="alert alert-info">
                                <h6><i class="fas fa-cookie-bite me-2"></i>Cookie Extraction Steps:</h6>
                                <ol>
                                    <li>In your XactAnalysis browser window, press <kbd>F12</kbd> to open Developer Tools</li>
                                    <li>Go to the <strong>"Application"</strong> tab (or <strong>"Storage"</strong> in Firefox)</li>
                                    <li>Click on <strong>"Cookies"</strong> in the left sidebar</li>
                                    <li>Click on <strong>"https://www.xactanalysis.com"</strong></li>
                                    <li>Copy ALL the cookies and paste them below</li>
                                </ol>
                            </div>
                            
                            <div class="mb-3">
                                <label for="cookies-text" class="form-label">
                                    <i class="fas fa-paste me-2"></i>Paste Cookies Here:
                                </label>
                                <textarea id="cookies-text" class="form-control cookie-textarea" 
                                         placeholder="Paste cookies in any format:&#10;&#10;Format 1: Name: cookie_name | Value: cookie_value&#10;Format 2: cookie_name=cookie_value&#10;Format 3: JSON format from browser export&#10;&#10;Example:&#10;Name: session_id | Value: abc123&#10;Name: auth_token | Value: xyz789"></textarea>
                            </div>
                            
                            <div class="d-grid">
                                <button id="load-cookies-btn" class="btn btn-primary">
                                    <i class="fas fa-key me-2"></i>Load Session
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Step 4: Process Claims -->
                <div class="card step-card">
                    <div class="card-header bg-light">
                        <h5 class="card-title mb-0">
                            <span class="step-number">4</span>
                            <span class="ms-2">Process Claims</span>
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="single-claim" class="form-label">Process Single Claim:</label>
                            <div class="input-group">
                                <input type="text" class="form-control" id="single-claim" placeholder="Enter claim number">
                                <button id="process-single-btn" class="btn btn-outline-success" disabled>
                                    <i class="fas fa-search me-1"></i>Process
                                </button>
                            </div>
                        </div>
                        <div class="d-grid gap-2">
                            <button id="process-next-btn" class="btn btn-success" disabled>
                                <i class="fas fa-step-forward me-2"></i>Process Next Claim
                            </button>
                            <button id="process-all-btn" class="btn btn-danger" disabled>
                                <i class="fas fa-play-circle me-2"></i>Process All Claims Automatically
                            </button>
                        </div>
                        <div class="mt-3">
                            <small class="text-muted">
                                <i class="fas fa-info-circle me-1"></i>
                                The system will automatically search for claims with 3-year date range, 
                                navigate to notes, and save PDFs with proper naming conventions.
                            </small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Right Panel - Status and Progress -->
            <div class="col-md-4">
                <!-- Progress Overview -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-chart-pie me-2"></i>Progress Overview
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row text-center">
                            <div class="col-4">
                                <h3 id="total-claims" class="text-primary">0</h3>
                                <p class="text-muted">Total</p>
                            </div>
                            <div class="col-4">
                                <h3 id="completed-claims" class="text-success">0</h3>
                                <p class="text-muted">Completed</p>
                            </div>
                            <div class="col-4">
                                <h3 id="pending-claims" class="text-warning">0</h3>
                                <p class="text-muted">Pending</p>
                            </div>
                        </div>
                        <div class="progress">
                            <div id="progress-bar" class="progress-bar bg-success" role="progressbar" style="width: 0%">
                                <span id="progress-text">0%</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Current Processing -->
                <div id="current-processing" class="card mb-4" style="display: none;">
                    <div class="card-header bg-info text-white">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-cog fa-spin me-2"></i>Currently Processing
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <strong>Claim:</strong> <span id="current-claim">-</span><br>
                                <small class="text-muted">Status: <span id="current-status">-</span></small>
                            </div>
                            <div class="spinner-border text-info" role="status">
                                <span class="visually-hidden">Processing...</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Activity Log -->
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-list me-2"></i>Activity Log
                        </h5>
                        <button id="clear-log-btn" class="btn btn-sm btn-outline-secondary">
                            <i class="fas fa-trash me-1"></i>Clear
                        </button>
                    </div>
                    <div class="card-body">
                        <div id="activity-log" class="activity-log">
                            <div class="log-entry text-muted">
                                <small><i class="fas fa-info-circle me-1"></i>Welcome! Upload a CSV file and login manually to get started.</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JavaScript -->
    <script>
        // Global state
        let isLoggedIn = false;
        let csvLoaded = false;
        let isProcessing = false;

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            setupEventListeners();
            updateStatus();
            updateTime();
            setInterval(updateTime, 1000);
        });

        function setupEventListeners() {
            // File upload
            const fileInput = document.getElementById('csv-file-input');
            const uploadArea = document.getElementById('upload-area');

            fileInput.addEventListener('change', handleFileSelect);
            
            // Drag and drop
            uploadArea.addEventListener('dragover', handleDragOver);
            uploadArea.addEventListener('dragleave', handleDragLeave);
            uploadArea.addEventListener('drop', handleDrop);

            // Login buttons
            document.getElementById('start-login-btn').addEventListener('click', handleStartLogin);
            document.getElementById('try-saved-session-btn').addEventListener('click', handleTrySavedSession);
            document.getElementById('extract-session-btn').addEventListener('click', handleExtractSession);
            document.getElementById('load-cookies-btn').addEventListener('click', handleLoadCookies);

            // Processing buttons
            document.getElementById('process-single-btn').addEventListener('click', handleProcessSingle);
            document.getElementById('process-next-btn').addEventListener('click', handleProcessNext);
            document.getElementById('process-all-btn').addEventListener('click', handleProcessAll);
            document.getElementById('clear-log-btn').addEventListener('click', clearLog);
        }

        function handleFileSelect(event) {
            const file = event.target.files[0];
            if (file) {
                uploadFile(file);
            }
        }

        function handleDragOver(event) {
            event.preventDefault();
            event.currentTarget.classList.add('dragover');
        }

        function handleDragLeave(event) {
            event.currentTarget.classList.remove('dragover');
        }

        function handleDrop(event) {
            event.preventDefault();
            event.currentTarget.classList.remove('dragover');
            
            const files = event.dataTransfer.files;
            if (files.length > 0) {
                uploadFile(files[0]);
            }
        }

        function uploadFile(file) {
            if (!file.name.toLowerCase().endsWith('.csv')) {
                addLogEntry('error', 'Please select a CSV file');
                return;
            }

            const formData = new FormData();
            formData.append('csv_file', file);

            addLogEntry('info', `Uploading file: ${file.name}`);

            fetch('/api/upload_csv', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    csvLoaded = true;
                    updateFileInfo(data);
                    addLogEntry('success', `CSV file loaded successfully: ${data.summary.total_claims} claims found`);
                    updateButtons();
                } else {
                    addLogEntry('error', `Failed to load CSV: ${data.message}`);
                }
            })
            .catch(error => {
                addLogEntry('error', `Upload error: ${error.message}`);
            });
        }

        function updateFileInfo(data) {
            const fileInfo = document.getElementById('file-info');
            const fileName = document.getElementById('file-name');
            const fileClaims = document.getElementById('file-claims');

            fileName.textContent = data.file_path.split('/').pop();
            fileClaims.textContent = `${data.summary.total_claims} total, ${data.summary.completed_claims} completed, ${data.summary.pending_claims} pending`;
            
            fileInfo.style.display = 'block';
            updateProgress(data.summary);
        }

        function handleStartLogin() {
            addLogEntry('info', 'Opening XactAnalysis for manual login...');
            
            fetch('/api/start_manual_login', {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    addLogEntry('success', 'Browser opened for login');
                    document.getElementById('login-instructions').style.display = 'block';
                    document.getElementById('extract-session-btn').disabled = false;
                } else {
                    addLogEntry('error', `Failed to start login: ${data.message}`);
                }
            })
            .catch(error => {
                addLogEntry('error', `Login start error: ${error.message}`);
            });
        }

        function handleTrySavedSession() {
            addLogEntry('info', 'Trying saved session...');
            
            fetch('/api/load_saved_session', {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    isLoggedIn = true;
                    updateLoginStatus(true);
                    addLogEntry('success', 'Saved session loaded successfully');
                    updateButtons();
                } else {
                    addLogEntry('warning', `Saved session failed: ${data.message}`);
                }
            })
            .catch(error => {
                addLogEntry('error', `Saved session error: ${error.message}`);
            });
        }

        function handleExtractSession() {
            fetch('/api/extract_session', {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    document.getElementById('cookie-instructions').style.display = 'block';
                    addLogEntry('info', 'Cookie extraction instructions displayed');
                } else {
                    addLogEntry('error', `Failed to get instructions: ${data.message}`);
                }
            })
            .catch(error => {
                addLogEntry('error', `Extract session error: ${error.message}`);
            });
        }

        function handleLoadCookies() {
            const cookiesText = document.getElementById('cookies-text').value.trim();
            
            if (!cookiesText) {
                addLogEntry('error', 'Please paste cookies first');
                return;
            }

            addLogEntry('info', 'Loading session cookies...');

            fetch('/api/load_cookies', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ cookies_text: cookiesText })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    isLoggedIn = true;
                    updateLoginStatus(true);
                    addLogEntry('success', `Session loaded successfully: ${data.cookies_loaded} cookies`);
                    updateButtons();
                    document.getElementById('cookies-text').value = '';
                } else {
                    addLogEntry('error', `Failed to load session: ${data.message}`);
                }
            })
            .catch(error => {
                addLogEntry('error', `Load cookies error: ${error.message}`);
            });
        }

        function handleProcessSingle() {
            const claimNumber = document.getElementById('single-claim').value.trim();
            if (!claimNumber) {
                addLogEntry('error', 'Please enter a claim number');
                return;
            }

            processClaim(claimNumber);
        }

        function handleProcessNext() {
            addLogEntry('info', 'Processing next pending claim...');
            
            fetch('/api/process_next', {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    if (data.completed) {
                        addLogEntry('info', 'No more pending claims to process');
                    } else {
                        addLogEntry('success', `Successfully processed claim: ${data.claim_number}`);
                        updateStatus();
                    }
                } else {
                    addLogEntry('error', `Failed to process claim: ${data.message}`);
                }
            })
            .catch(error => {
                addLogEntry('error', `Process error: ${error.message}`);
            });
        }

        function handleProcessAll() {
            if (!confirm('This will process ALL pending claims automatically. Continue?')) {
                return;
            }

            isProcessing = true;
            updateButtons();
            showProcessingStatus('Processing all claims...', 'Starting batch processing');

            addLogEntry('info', 'Starting automatic processing of all claims...');
            
            fetch('/api/process_all', {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                isProcessing = false;
                hideProcessingStatus();
                updateButtons();
                
                if (data.success) {
                    addLogEntry('success', `Batch processing completed: ${data.processed_count} successful, ${data.failed_count} failed`);
                    if (data.failed_count > 0) {
                        data.failed_claims.forEach(failed => {
                            addLogEntry('warning', `Failed claim ${failed.claim_number}: ${failed.error}`);
                        });
                    }
                    updateStatus();
                } else {
                    addLogEntry('error', `Batch processing failed: ${data.message}`);
                }
            })
            .catch(error => {
                isProcessing = false;
                hideProcessingStatus();
                updateButtons();
                addLogEntry('error', `Batch processing error: ${error.message}`);
            });
        }

        function processClaim(claimNumber) {
            showProcessingStatus(claimNumber, 'Searching for claim...');
            
            fetch('/api/process_claim', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ claim_number: claimNumber })
            })
            .then(response => response.json())
            .then(data => {
                hideProcessingStatus();
                
                if (data.success) {
                    addLogEntry('success', `Successfully processed claim ${claimNumber}: ${data.pdfs_generated} PDF(s) generated`);
                    updateStatus();
                } else {
                    addLogEntry('error', `Failed to process claim ${claimNumber}: ${data.message}`);
                }
            })
            .catch(error => {
                hideProcessingStatus();
                addLogEntry('error', `Process error: ${error.message}`);
            });
        }

        function updateStatus() {
            fetch('/api/status')
            .then(response => response.json())
            .then(data => {
                if (data.success && data.status.csv_summary) {
                    updateProgress(data.status.csv_summary);
                }
            })
            .catch(error => {
                console.error('Status update error:', error);
            });
        }

        function updateProgress(summary) {
            document.getElementById('total-claims').textContent = summary.total_claims;
            document.getElementById('completed-claims').textContent = summary.completed_claims;
            document.getElementById('pending-claims').textContent = summary.pending_claims;
            
            const progressBar = document.getElementById('progress-bar');
            const progressText = document.getElementById('progress-text');
            const percent = summary.progress_percent;
            
            progressBar.style.width = `${percent}%`;
            progressText.textContent = `${percent}%`;
        }

        function updateLoginStatus(loggedIn) {
            const statusElement = document.getElementById('login-status');
            
            if (loggedIn) {
                statusElement.innerHTML = '<span class="status-indicator status-connected"></span>Logged In';
                statusElement.className = 'badge bg-success me-2';
            } else {
                statusElement.innerHTML = '<span class="status-indicator status-disconnected"></span>Not Logged In';
                statusElement.className = 'badge bg-secondary me-2';
            }
        }

        function updateButtons() {
            document.getElementById('start-login-btn').disabled = !csvLoaded;
            document.getElementById('extract-session-btn').disabled = !csvLoaded;
            document.getElementById('process-single-btn').disabled = !isLoggedIn || isProcessing;
            document.getElementById('process-next-btn').disabled = !isLoggedIn || isProcessing;
            document.getElementById('process-all-btn').disabled = !isLoggedIn || isProcessing;
        }

        function showProcessingStatus(claim, status) {
            document.getElementById('current-claim').textContent = claim;
            document.getElementById('current-status').textContent = status;
            document.getElementById('current-processing').style.display = 'block';
        }

        function hideProcessingStatus() {
            document.getElementById('current-processing').style.display = 'none';
        }

        function addLogEntry(type, message) {
            const log = document.getElementById('activity-log');
            const entry = document.createElement('div');
            entry.className = 'log-entry';
            
            const timestamp = new Date().toLocaleTimeString();
            let icon, className;
            
            switch(type) {
                case 'success':
                    icon = 'fas fa-check-circle';
                    className = 'text-success';
                    break;
                case 'error':
                    icon = 'fas fa-exclamation-circle';
                    className = 'text-danger';
                    break;
                case 'warning':
                    icon = 'fas fa-exclamation-triangle';
                    className = 'text-warning';
                    break;
                default:
                    icon = 'fas fa-info-circle';
                    className = 'text-info';
            }
            
            entry.innerHTML = `<small class="${className}"><i class="${icon} me-1"></i>[${timestamp}] ${message}</small>`;
            log.appendChild(entry);
            log.scrollTop = log.scrollHeight;
        }

        function clearLog() {
            document.getElementById('activity-log').innerHTML = '';
        }

        function updateTime() {
            document.getElementById('current-time').textContent = new Date().toLocaleString();
        }
    </script>
</body>
</html>
